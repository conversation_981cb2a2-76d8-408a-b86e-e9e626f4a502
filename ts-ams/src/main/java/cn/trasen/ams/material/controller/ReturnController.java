package cn.trasen.ams.material.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.material.model.Return;
import cn.trasen.ams.material.service.ReturnService;
import cn.trasen.ams.material.bean.returnOrder.ReturnInsertReq;
import cn.trasen.ams.material.bean.returnOrder.ReturnDetailResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.List;

/**
 * @ClassName ReturnController
 * @Description TODO
 * @date 2025年8月7日 下午4:21:34
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "ReturnController")
public class ReturnController {

	private transient static final Logger logger = LoggerFactory.getLogger(ReturnController.class);

	@Autowired
	private ReturnService returnService;



	/**
	 * @Title selectReturnList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<Return>
	 * @date 2025年8月7日 下午4:21:34
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/material/return/list")
	public DataSet<Return> selectReturnList(Page page, Return record) {
		return returnService.getDataSetList(page, record);
	}

	/**
	 * @Title selectReturnById
	 * @Description 根据ID查询退货单详情
	 * @param id
	 * @param direction
	 * @param name
	 * @return PlatformResult<ReturnDetailResp>
	 * @date 2025年8月7日 下午5:30:00
	 * <AUTHOR>
	 */
	@ApiOperation(value = "退货单详情", notes = "退货单详情")
	@GetMapping("/api/material/return/{id}")
	public PlatformResult<ReturnDetailResp> selectReturnById(@PathVariable String id,
			@RequestParam(value = "direction", defaultValue = "current") String direction,
			@RequestParam(required = false) String name) {
		try {
			ReturnDetailResp record = returnService.getReturnDetail(id, direction, name);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title insertReturn
	 * @Description 新增退货单
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025年8月7日 下午4:45:00
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增退货单", notes = "新增退货单")
	@PostMapping("/api/material/return/insert")
	public PlatformResult<String> insertReturn(@Valid @RequestBody ReturnInsertReq record) {
		try {
			String returnId = returnService.insert(record);
			return PlatformResult.success(returnId);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title editReturn
	 * @Description 编辑退货单
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025年8月7日 下午4:45:00
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑退货单", notes = "编辑退货单")
	@PostMapping("/api/material/return/edit")
	public PlatformResult<String> editReturn(@Valid @RequestBody ReturnInsertReq record) {
		try {
			String returnId = returnService.edit(record);
			return PlatformResult.success(returnId);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title removeReturn
	 * @Description 删除退货单
	 * @param returnId
	 * @return PlatformResult<String>
	 * @date 2025年8月7日 下午4:45:00
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除退货单", notes = "删除退货单")
	@PostMapping("/api/material/return/remove")
	public PlatformResult<String> removeReturn(@RequestParam String returnId) {
		try {
			returnService.remove(returnId);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title batchRemoveReturn
	 * @Description 批量删除退货单
	 * @param returnIdList
	 * @return PlatformResult<String>
	 * @date 2025年8月7日 下午4:45:00
	 * <AUTHOR>
	 */
	@ApiOperation(value = "批量删除退货单", notes = "批量删除退货单")
	@PostMapping("/api/material/return/batchRemove")
	public PlatformResult<String> batchRemoveReturn(@RequestBody List<String> returnIdList) {
		try {
			returnService.batchRemove(returnIdList);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title confirmReturn
	 * @Description 确认退货单
	 * @param returnId
	 * @return PlatformResult<String>
	 * @date 2025年8月7日 下午4:45:00
	 * <AUTHOR>
	 */
	@ApiOperation(value = "确认退货单", notes = "确认退货单")
	@PostMapping("/api/material/return/confirm")
	public PlatformResult<String> confirmReturn(@RequestParam String returnId) {
		try {
			returnService.confirm(returnId);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title batchConfirmReturn
	 * @Description 批量确认退货单
	 * @param returnIdList
	 * @return PlatformResult<String>
	 * @date 2025年8月7日 下午4:45:00
	 * <AUTHOR>
	 */
	@ApiOperation(value = "批量确认退货单", notes = "批量确认退货单")
	@PostMapping("/api/material/return/batchConfirm")
	public PlatformResult<String> batchConfirmReturn(@RequestBody List<String> returnIdList) {
		try {
			returnService.batchConfirm(returnIdList);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title rollbackConfirmReturn
	 * @Description 撤销确认退货单
	 * @param returnId
	 * @return PlatformResult<String>
	 * @date 2025年8月7日 下午4:45:00
	 * <AUTHOR>
	 */
	@ApiOperation(value = "撤销确认退货单", notes = "撤销确认退货单")
	@PostMapping("/api/material/return/rollbackConfirm")
	public PlatformResult<String> rollbackConfirmReturn(@RequestParam String returnId) {
		try {
			returnService.rollbackConfirm(returnId);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title rollbackBatchConfirmReturn
	 * @Description 批量撤销确认退货单
	 * @param returnIdList
	 * @return PlatformResult<String>
	 * @date 2025年8月7日 下午4:45:00
	 * <AUTHOR>
	 */
	@ApiOperation(value = "批量撤销确认退货单", notes = "批量撤销确认退货单")
	@PostMapping("/api/material/return/rollbackBatchConfirm")
	public PlatformResult<String> rollbackBatchConfirmReturn(@RequestBody List<String> returnIdList) {
		try {
			returnService.rollbackBatchConfirm(returnIdList);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
}
